[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "edgecli"
version = "1.0.0"
description = "A Python tool for managing XRay configurations in client/server mode"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "EdgeCLI Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "EdgeCLI Team", email = "<EMAIL>"}
]
keywords = ["xray", "proxy", "minecraft", "cli", "network"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Console",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: Proxy Servers",
    "Topic :: System :: Networking",
    "Topic :: Utilities",
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "colorama>=0.4.4",
    "cryptography>=3.4.8",
    "dnspython>=2.1.0",
    "requests>=2.25.1",
    "pyyaml>=5.4.1",
    "rich>=10.0.0",
    "pyperclip>=1.8.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "isort>=5.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "nuitka>=2.4.8",
]
build = [
    "nuitka>=2.4.8",
    "setuptools>=61.0",
    "wheel",
]

[project.urls]
Homepage = "https://github.com/lilingfengdev/EdgeCLI"
Repository = "https://github.com/lilingfengdev/EdgeCLI"
Documentation = "https://github.com/lilingfengdev/EdgeCLI/blob/main/README.md"
"Bug Tracker" = "https://github.com/lilingfengdev/EdgeCLI/issues"
Changelog = "https://github.com/lilingfengdev/EdgeCLI/releases"

[project.scripts]
edgecli = "edgecli.frontend.cli.main_cli:main"

[tool.setuptools]
packages = ["edgecli"]

[tool.setuptools.package-data]
edgecli = ["**/*.json", "**/*.yaml", "**/*.yml"]

# Black 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["edgecli"]

# pytest 测试配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Coverage 配置
[tool.coverage.run]
source = ["edgecli"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "build.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# MyPy 类型检查配置
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "colorama.*",
    "dns.*",
    "pyperclip.*",
]
ignore_missing_imports = true

# Nuitka 构建配置
[tool.nuitka]
main = "main.py"
standalone = true
onefile = true
assume-yes-for-downloads = true
enable-plugin = "anti-bloat"
output-dir = "dist"
output-filename = "EdgeCLI"

# 包含的包
include-package = [
    "edgecli",
    "click",
    "rich",
    "colorama",
    "cryptography",
    "dns",
    "requests",
    "yaml",
    "pyperclip",
]

# 排除的包
nofollow-import-to = [
    "tkinter",
    "matplotlib",
    "numpy",
    "scipy",
    "pandas",
    "PIL",
    "cv2",
]
