{"name": "LocalTest", "frontend_host": "127.0.0.1", "backend_ip": "127.0.0.1", "backend_port": 25565, "client_id": "abc3137e-bb3e-4218-86e5-30a3abaaa8b6", "certificate": ["-----BEGIN CERTIFICATE-----", "MIIBoDCCAUWgAwIBAgIUIoaW3tfUGtyIJ8heoV0ER/AjBPYwCgYIKoZIzj0EAwIw", "JjERMA8GA1UECgwIWHJheSBJbmMxETAPBgNVBAMMCFhyYXkgSW5jMB4XDTI1MDgw", "NDEzNDM1MloXDTI1MTEwMjEzNDM1MlowJjERMA8GA1UECgwIWHJheSBJbmMxETAP", "BgNVBAMMCFhyYXkgSW5jMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEpz6AGBkX", "crnYzNXpjqY1WQk23OrFFDUnePMB+PHZpO1edlM/ya4H7myb5OPiVrs4HqEqZwn2", "RpdgEJ87c5F8zKNRME8wFAYDVR0RBA0wC4IJMTI3LjAuMC4xMA4GA1UdDwEB/wQE", "AwICpDAWBgNVHSUBAf8EDDAKBggrBgEFBQcDATAPBgNVHRMBAf8EBTADAQH/MAoG", "CCqGSM49BAMCA0kAMEYCIQCnXMuhyFvnHnEkKpm23rSnlhV/d8pTcUMQ1t79Vhtx", "UAIhANCOkOXJdoFP2tuN27zlT8cQ7ITd8PxHgc4aJQCQcYOC", "-----END CERTIFICATE-----"], "private_key": ["-----BEGIN PRIVATE KEY-----", "MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQgQjziRGXfn1NbWqm8", "KFzYroi5qyB77wX7moC9AjTb/5ahRANCAASnPoAYGRdyudjM1emOpjVZCTbc6sUU", "NSd48wH48dmk7V52Uz/JrgfubJvk4+JWuzgeoSpnCfZGl2AQnztzkXzM", "-----END PRIVATE KEY-----"]}