name: Release Build

permissions:
  contents: write

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build (e.g., v1.0.0)'
        required: false
        default: ''
      prerelease:
        description: 'Mark as prerelease'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'

env:
  PYTHON_VERSION: '3.13'
  NUITKA_VERSION: '2.7.12'
  UPX_VERSION: '5.0.2'

jobs:
  build:
    name: Build on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-latest
            platform: windows
            arch: x64
            executable_suffix: .exe
          - os: ubuntu-latest
            platform: linux
            arch: x64
            executable_suffix: ''
          - os: macos-latest
            platform: macos
            arch: x64
            executable_suffix: ''

    steps:

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get version
        id: version
        shell: bash
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" && "${{ github.event.inputs.version }}" != "" ]]; then
            VERSION="${{ github.event.inputs.version }}"
          elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION="release-$(git rev-parse --short HEAD)"
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Building version: $VERSION"

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'


      - name: Setup MSVC environment (Windows)
        if: matrix.platform == 'windows'
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x64

      - name: Install system dependencies (Ubuntu)
        if: matrix.platform == 'linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential zlib1g-dev libffi-dev upx-ucl

      - name: Install system dependencies (macOS)
        if: matrix.platform == 'macos'
        run: |
          brew install zlib upx

      - name: Install system dependencies (Windows)
        if: matrix.platform == 'windows'
        run: |
          # 安装 UPX
          $upxVersion = "${{ env.UPX_VERSION }}"
          $upxUrl = "https://github.com/upx/upx/releases/download/v$upxVersion/upx-$upxVersion-win64.zip"
          Invoke-WebRequest -Uri $upxUrl -OutFile "upx.zip"
          Expand-Archive -Path "upx.zip" -DestinationPath "."
          $upxPath = "upx-$upxVersion-win64/upx.exe"
          Move-Item $upxPath "C:/Windows/System32/upx.exe"
          # 验证安装
          upx --version

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install nuitka==${{ env.NUITKA_VERSION }}
          pip install -r requirements.txt

      - name: Build
        run: |
          python build.py --version ${{ steps.version.outputs.version }} --clean

      - name: Test executable
        shell: bash
        run: |
          cd dist
          if [[ "${{ matrix.platform }}" == "windows" ]]; then
            ./EdgeCLI.exe --version || echo "Version check failed, but executable exists"
          else
            ./EdgeCLI --version || echo "Version check failed, but executable exists"
          fi
          echo "✅ Executable test completed"

      - name: Create archive
        shell: bash
        run: |
          cd dist
          if [[ "${{ matrix.platform }}" == "windows" ]]; then
            7z a -tzip EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.zip EdgeCLI${{ matrix.executable_suffix }}
          else
            tar -czf EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.tar.gz EdgeCLI${{ matrix.executable_suffix }}
          fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: EdgeCLI-${{ matrix.platform }}-${{ matrix.arch }}
          path: |
            dist/EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.*
          retention-days: 30

      - name: Upload to release
        if: startsWith(github.ref, 'refs/tags/') || github.event_name == 'workflow_dispatch'
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: EdgeCLI ${{ steps.version.outputs.version }}
          draft: false
          prerelease: ${{ github.event.inputs.prerelease == 'true' || contains(steps.version.outputs.version, 'alpha') || contains(steps.version.outputs.version, 'beta') || contains(steps.version.outputs.version, 'rc') }}
          files: |
            dist/EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}


